<?php

defined('BASEPATH') or exit('No direct script access allowed');

$aColumns = [
    'p.image',
    'p.name',
    'p.sku',
    'c.name as category_name',
    'p.sale_price',
    'p.cost_price',
    'COALESCE(SUM(i.quantity), 0) as stock_quantity',
    'p.status',
    'p.id'
];

$sIndexColumn = 'p.id';
$sTable       = db_prefix() . 'pos_products p';

$join = [
    'LEFT JOIN ' . db_prefix() . 'pos_categories c ON c.id = p.category_id',
    'LEFT JOIN ' . db_prefix() . 'pos_inventory i ON i.product_id = p.id'
];

$where = [];

// Category filter
if ($this->input->post('category') && $this->input->post('category') != '') {
    $where[] = 'AND p.category_id = ' . $this->db->escape_str($this->input->post('category'));
}

// Status filter
if ($this->input->post('status') && $this->input->post('status') != '') {
    $where[] = 'AND p.status = ' . $this->db->escape_str($this->input->post('status'));
}

// Stock filter
if ($this->input->post('stock') && $this->input->post('stock') != '') {
    $stock_filter = $this->input->post('stock');
    switch ($stock_filter) {
        case 'in_stock':
            $where[] = 'AND COALESCE(SUM(i.quantity), 0) > p.low_stock_threshold';
            break;
        case 'low_stock':
            $where[] = 'AND COALESCE(SUM(i.quantity), 0) <= p.low_stock_threshold AND COALESCE(SUM(i.quantity), 0) > 0';
            break;
        case 'out_of_stock':
            $where[] = 'AND COALESCE(SUM(i.quantity), 0) = 0';
            break;
    }
}

$groupBy = 'p.id';

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    'p.id',
    'p.name',
    'p.sku',
    'p.barcode',
    'p.description',
    'p.sale_price',
    'p.cost_price',
    'p.image',
    'p.status',
    'p.low_stock_threshold',
    'p.track_inventory',
    'c.name as category_name'
], null, $groupBy);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    // Image
    $image_url = '';
    if (!empty($aRow['image'])) {
        $image_url = base_url('uploads/pos_products/' . $aRow['image']);
    } else {
        $image_url = base_url('assets/images/no-image.png');
    }
    
    $row[] = '<img src="' . $image_url . '" alt="' . _l('product_image') . '" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">';

    // Product name with link
    $name = '<a href="' . admin_url('pos_inventory/product/' . $aRow['id']) . '" class="text-primary">';
    $name .= '<strong>' . $aRow['name'] . '</strong>';
    $name .= '</a>';
    if (!empty($aRow['description'])) {
        $name .= '<br><small class="text-muted">' . character_limiter($aRow['description'], 50) . '</small>';
    }
    $row[] = $name;

    // SKU with barcode
    $sku = '<strong>' . $aRow['sku'] . '</strong>';
    if (!empty($aRow['barcode'])) {
        $sku .= '<br><small class="text-muted">Barcode: ' . $aRow['barcode'] . '</small>';
    }
    $row[] = $sku;

    // Category
    $row[] = $aRow['category_name'] ?: '<span class="text-muted">' . _l('uncategorized') . '</span>';

    // Sale price
    $row[] = '<strong>' . app_format_money($aRow['sale_price'], get_base_currency()) . '</strong>';

    // Cost price
    $cost_price = app_format_money($aRow['cost_price'], get_base_currency());
    if ($aRow['cost_price'] > 0 && $aRow['sale_price'] > 0) {
        $profit = $aRow['sale_price'] - $aRow['cost_price'];
        $margin = ($profit / $aRow['sale_price']) * 100;
        $cost_price .= '<br><small class="text-muted">Margin: ' . number_format($margin, 1) . '%</small>';
    }
    $row[] = $cost_price;

    // Stock quantity
    $stock_quantity = (int)$aRow['stock_quantity'];
    $low_threshold = (int)$aRow['low_stock_threshold'];
    
    if (!$aRow['track_inventory']) {
        $stock_display = '<span class="label label-info">' . _l('not_tracked') . '</span>';
    } elseif ($stock_quantity == 0) {
        $stock_display = '<span class="label label-danger">' . _l('out_of_stock') . '</span>';
    } elseif ($stock_quantity <= $low_threshold) {
        $stock_display = '<span class="label label-warning">' . $stock_quantity . ' ' . _l('low_stock') . '</span>';
    } else {
        $stock_display = '<span class="label label-success">' . $stock_quantity . ' ' . _l('in_stock') . '</span>';
    }
    
    $row[] = $stock_display;

    // Status
    if ($aRow['status'] == 1) {
        $status = '<span class="label label-success">' . _l('active') . '</span>';
    } else {
        $status = '<span class="label label-default">' . _l('inactive') . '</span>';
    }
    $row[] = $status;

    // Actions
    $options = '';
    
    if (has_permission('pos_products', '', 'view')) {
        $options .= '<a href="' . admin_url('pos_inventory/product/' . $aRow['id']) . '" class="btn btn-default btn-icon" data-toggle="tooltip" title="' . _l('view') . '"><i class="fa fa-eye"></i></a>';
    }
    
    if (has_permission('pos_products', '', 'edit')) {
        $options .= '<a href="' . admin_url('pos_inventory/product/' . $aRow['id']) . '" class="btn btn-default btn-icon" data-toggle="tooltip" title="' . _l('edit') . '"><i class="fa fa-pencil-square-o"></i></a>';
    }
    
    if (has_permission('pos_inventory', '', 'edit')) {
        $options .= '<a href="' . admin_url('pos_inventory/stock_adjustment?product=' . $aRow['id']) . '" class="btn btn-info btn-icon" data-toggle="tooltip" title="' . _l('adjust_stock') . '"><i class="fa fa-cubes"></i></a>';
    }
    
    if (has_permission('pos_products', '', 'create')) {
        $options .= '<a href="' . admin_url('pos_inventory/product?duplicate=' . $aRow['id']) . '" class="btn btn-success btn-icon" data-toggle="tooltip" title="' . _l('duplicate') . '"><i class="fa fa-copy"></i></a>';
    }
    
    if (has_permission('pos_products', '', 'delete')) {
        $options .= '<a href="' . admin_url('pos_inventory/delete_product/' . $aRow['id']) . '" class="btn btn-danger btn-icon _delete" data-toggle="tooltip" title="' . _l('delete') . '"><i class="fa fa-remove"></i></a>';
    }

    $row[] = $options;

    $output['aaData'][] = $row;
}
