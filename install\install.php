<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Advanced POS & Central Inventory Module Installation
 * 
 * Enhanced installation script with comprehensive error handling,
 * table verification, and automatic migration record creation
 */

$CI = &get_instance();

try {
    // Log installation start
    log_message('info', 'Starting POS Inventory module installation');

    // Verify database connection
    if (!$CI->db->conn_id) {
        throw new Exception('Database connection failed');
    }

    // Create upload directories
    pos_inventory_create_upload_directories();

    // Install database tables
    pos_inventory_install_database_tables();

    // Insert default data
    pos_inventory_insert_default_data();

    // Create default options
    pos_inventory_create_default_options();

    // Verify installation
    pos_inventory_verify_installation();

    // Create migration record
    pos_inventory_create_migration_record();

    log_message('info', 'POS Inventory module installation completed successfully');
    echo "Advanced POS & Central Inventory module installed successfully!";

} catch (Exception $e) {
    log_message('error', 'POS Inventory module installation failed: ' . $e->getMessage());
    echo "Installation failed: " . $e->getMessage();
    throw $e; // Re-throw to prevent module activation
}

/**
 * Create upload directories
 */
function pos_inventory_create_upload_directories()
{
    $directories = [
        FCPATH . 'modules/pos_inventory/uploads/',
        FCPATH . 'modules/pos_inventory/uploads/products/',
        FCPATH . 'modules/pos_inventory/uploads/categories/',
        FCPATH . 'modules/pos_inventory/uploads/suppliers/',
        FCPATH . 'modules/pos_inventory/uploads/receipts/'
    ];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("Failed to create directory: $dir");
            }
            
            // Create index.html for security
            $index_file = $dir . 'index.html';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><p>Directory access is forbidden.</p></body></html>');
            }
        }
    }

    log_message('info', 'Created upload directories');
}

/**
 * Install database tables
 */
function pos_inventory_install_database_tables()
{
    $CI = &get_instance();
    
    // Create pos_categories table
    if (!$CI->db->table_exists(db_prefix() . 'pos_categories')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(191) NOT NULL,
            `description` text,
            `parent_id` int(11) DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `sort_order` int(11) DEFAULT 0,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `parent_id` (`parent_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_products table
    if (!$CI->db->table_exists(db_prefix() . 'pos_products')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(191) NOT NULL,
            `description` text,
            `sku` varchar(100) NOT NULL,
            `barcode` varchar(100) DEFAULT NULL,
            `category_id` int(11) DEFAULT NULL,
            `unit` varchar(50) DEFAULT NULL,
            `cost_price` decimal(15,2) DEFAULT 0.00,
            `sale_price` decimal(15,2) NOT NULL DEFAULT 0.00,
            `weight` decimal(8,2) DEFAULT NULL,
            `dimensions` varchar(100) DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `track_inventory` tinyint(1) NOT NULL DEFAULT 1,
            `allow_backorders` tinyint(1) NOT NULL DEFAULT 0,
            `low_stock_threshold` int(11) DEFAULT 5,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `woocommerce_id` int(11) DEFAULT NULL,
            `shopify_id` bigint(20) DEFAULT NULL,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `sku` (`sku`),
            KEY `category_id` (`category_id`),
            KEY `barcode` (`barcode`),
            KEY `status` (`status`),
            KEY `woocommerce_id` (`woocommerce_id`),
            KEY `shopify_id` (`shopify_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_product_variants table
    if (!$CI->db->table_exists(db_prefix() . 'pos_product_variants')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_product_variants` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `product_id` int(11) NOT NULL,
            `name` varchar(191) NOT NULL,
            `sku` varchar(100) DEFAULT NULL,
            `barcode` varchar(100) DEFAULT NULL,
            `cost_price` decimal(15,2) DEFAULT NULL,
            `sale_price` decimal(15,2) DEFAULT NULL,
            `weight` decimal(8,2) DEFAULT NULL,
            `dimensions` varchar(100) DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `woocommerce_id` int(11) DEFAULT NULL,
            `shopify_id` bigint(20) DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `product_id` (`product_id`),
            KEY `sku` (`sku`),
            KEY `barcode` (`barcode`),
            KEY `woocommerce_id` (`woocommerce_id`),
            KEY `shopify_id` (`shopify_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_locations table
    if (!$CI->db->table_exists(db_prefix() . 'pos_locations')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_locations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(191) NOT NULL,
            `address` text,
            `phone` varchar(50) DEFAULT NULL,
            `email` varchar(100) DEFAULT NULL,
            `manager_id` int(11) DEFAULT NULL,
            `is_default` tinyint(1) NOT NULL DEFAULT 0,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `manager_id` (`manager_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_inventory table
    if (!$CI->db->table_exists(db_prefix() . 'pos_inventory')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_inventory` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `product_id` int(11) NOT NULL,
            `variant_id` int(11) DEFAULT NULL,
            `location_id` int(11) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 0,
            `reserved_quantity` int(11) NOT NULL DEFAULT 0,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `product_variant_location` (`product_id`, `variant_id`, `location_id`),
            KEY `location_id` (`location_id`),
            KEY `quantity` (`quantity`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_stock_movements table
    if (!$CI->db->table_exists(db_prefix() . 'pos_stock_movements')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_stock_movements` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `product_id` int(11) NOT NULL,
            `variant_id` int(11) DEFAULT NULL,
            `location_id` int(11) NOT NULL,
            `movement_type` enum('in','out','transfer','adjustment') NOT NULL,
            `quantity` int(11) NOT NULL,
            `unit_cost` decimal(15,2) DEFAULT NULL,
            `reference_type` varchar(50) DEFAULT NULL,
            `reference_id` int(11) DEFAULT NULL,
            `notes` text,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `product_id` (`product_id`),
            KEY `location_id` (`location_id`),
            KEY `movement_type` (`movement_type`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_suppliers table
    if (!$CI->db->table_exists(db_prefix() . 'pos_suppliers')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_suppliers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(191) NOT NULL,
            `contact_person` varchar(100) DEFAULT NULL,
            `email` varchar(100) DEFAULT NULL,
            `phone` varchar(50) DEFAULT NULL,
            `address` text,
            `city` varchar(100) DEFAULT NULL,
            `state` varchar(100) DEFAULT NULL,
            `country` varchar(100) DEFAULT NULL,
            `postal_code` varchar(20) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `payment_terms` varchar(100) DEFAULT NULL,
            `notes` text,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create remaining tables
    pos_inventory_create_remaining_tables();

    // Add foreign key constraints
    pos_inventory_add_foreign_key_constraints();

    // Add performance indexes
    pos_inventory_add_performance_indexes();

    log_message('info', 'All database tables created successfully');
}

/**
 * Create remaining database tables
 */
function pos_inventory_create_remaining_tables()
{
    $CI = &get_instance();

    // Create pos_purchase_orders table
    if (!$CI->db->table_exists(db_prefix() . 'pos_purchase_orders')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_purchase_orders` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `po_number` varchar(50) NOT NULL,
            `supplier_id` int(11) NOT NULL,
            `location_id` int(11) NOT NULL,
            `order_date` date NOT NULL,
            `expected_delivery_date` date DEFAULT NULL,
            `status` enum('draft','pending','ordered','partially_received','received','cancelled') NOT NULL DEFAULT 'draft',
            `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
            `shipping_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
            `total` decimal(15,2) NOT NULL DEFAULT 0.00,
            `currency` varchar(10) NOT NULL DEFAULT 'USD',
            `notes` text,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `po_number` (`po_number`),
            KEY `supplier_id` (`supplier_id`),
            KEY `location_id` (`location_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_purchase_order_items table
    if (!$CI->db->table_exists(db_prefix() . 'pos_purchase_order_items')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_purchase_order_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_order_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `variant_id` int(11) DEFAULT NULL,
            `quantity` int(11) NOT NULL,
            `unit_cost` decimal(15,2) NOT NULL,
            `total_cost` decimal(15,2) NOT NULL,
            `received_quantity` int(11) NOT NULL DEFAULT 0,
            `notes` text,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `purchase_order_id` (`purchase_order_id`),
            KEY `product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_transactions table
    if (!$CI->db->table_exists(db_prefix() . 'pos_transactions')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_transactions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `transaction_number` varchar(50) NOT NULL,
            `transaction_type` enum('sale','return','exchange') NOT NULL DEFAULT 'sale',
            `customer_id` int(11) DEFAULT NULL,
            `location_id` int(11) NOT NULL,
            `cashier_id` int(11) NOT NULL,
            `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
            `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
            `total` decimal(15,2) NOT NULL DEFAULT 0.00,
            `payment_method` varchar(50) NOT NULL,
            `payment_status` enum('pending','paid','partial','refunded') NOT NULL DEFAULT 'pending',
            `notes` text,
            `receipt_printed` tinyint(1) NOT NULL DEFAULT 0,
            `email_sent` tinyint(1) NOT NULL DEFAULT 0,
            `transaction_date` datetime NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `transaction_number` (`transaction_number`),
            KEY `customer_id` (`customer_id`),
            KEY `location_id` (`location_id`),
            KEY `cashier_id` (`cashier_id`),
            KEY `transaction_type` (`transaction_type`),
            KEY `transaction_date` (`transaction_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_transaction_items table
    if (!$CI->db->table_exists(db_prefix() . 'pos_transaction_items')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_transaction_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `transaction_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `variant_id` int(11) DEFAULT NULL,
            `quantity` int(11) NOT NULL,
            `unit_price` decimal(15,2) NOT NULL,
            `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(15,2) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `transaction_id` (`transaction_id`),
            KEY `product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_held_orders table
    if (!$CI->db->table_exists(db_prefix() . 'pos_held_orders')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_held_orders` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `order_number` varchar(50) NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `location_id` int(11) NOT NULL,
            `cashier_id` int(11) NOT NULL,
            `order_data` longtext NOT NULL,
            `notes` text,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_number` (`order_number`),
            KEY `customer_id` (`customer_id`),
            KEY `location_id` (`location_id`),
            KEY `cashier_id` (`cashier_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_integrations table
    if (!$CI->db->table_exists(db_prefix() . 'pos_integrations')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_integrations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `platform` enum('woocommerce','shopify') NOT NULL,
            `name` varchar(191) NOT NULL,
            `api_url` varchar(255) NOT NULL,
            `api_key` varchar(255) NOT NULL,
            `api_secret` varchar(255) DEFAULT NULL,
            `access_token` varchar(255) DEFAULT NULL,
            `webhook_secret` varchar(255) DEFAULT NULL,
            `settings` longtext,
            `sync_products` tinyint(1) NOT NULL DEFAULT 1,
            `sync_inventory` tinyint(1) NOT NULL DEFAULT 1,
            `sync_orders` tinyint(1) NOT NULL DEFAULT 0,
            `auto_sync` tinyint(1) NOT NULL DEFAULT 0,
            `sync_frequency` int(11) NOT NULL DEFAULT 60,
            `last_sync` datetime DEFAULT NULL,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `platform` (`platform`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    // Create pos_sync_logs table
    if (!$CI->db->table_exists(db_prefix() . 'pos_sync_logs')) {
        $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_sync_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `integration_id` int(11) NOT NULL,
            `sync_type` varchar(50) NOT NULL,
            `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
            `records_processed` int(11) NOT NULL DEFAULT 0,
            `records_success` int(11) NOT NULL DEFAULT 0,
            `records_failed` int(11) NOT NULL DEFAULT 0,
            `error_message` text,
            `started_at` datetime DEFAULT NULL,
            `completed_at` datetime DEFAULT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `integration_id` (`integration_id`),
            KEY `sync_type` (`sync_type`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
    }

    log_message('info', 'Remaining database tables created');
}

/**
 * Add foreign key constraints
 */
function pos_inventory_add_foreign_key_constraints()
{
    $CI = &get_instance();

    try {
        $constraints = [
            'ALTER TABLE `' . db_prefix() . 'pos_products` ADD CONSTRAINT `pos_products_category_fk` FOREIGN KEY (`category_id`) REFERENCES `' . db_prefix() . 'pos_categories` (`id`) ON DELETE SET NULL',
            'ALTER TABLE `' . db_prefix() . 'pos_product_variants` ADD CONSTRAINT `pos_product_variants_product_fk` FOREIGN KEY (`product_id`) REFERENCES `' . db_prefix() . 'pos_products` (`id`) ON DELETE CASCADE',
            'ALTER TABLE `' . db_prefix() . 'pos_inventory` ADD CONSTRAINT `pos_inventory_product_fk` FOREIGN KEY (`product_id`) REFERENCES `' . db_prefix() . 'pos_products` (`id`) ON DELETE CASCADE',
            'ALTER TABLE `' . db_prefix() . 'pos_inventory` ADD CONSTRAINT `pos_inventory_variant_fk` FOREIGN KEY (`variant_id`) REFERENCES `' . db_prefix() . 'pos_product_variants` (`id`) ON DELETE CASCADE',
            'ALTER TABLE `' . db_prefix() . 'pos_inventory` ADD CONSTRAINT `pos_inventory_location_fk` FOREIGN KEY (`location_id`) REFERENCES `' . db_prefix() . 'pos_locations` (`id`) ON DELETE CASCADE'
        ];

        foreach ($constraints as $constraint) {
            try {
                $CI->db->query($constraint);
            } catch (Exception $e) {
                // Constraint may already exist, log but continue
                log_message('warning', 'Foreign key constraint may already exist: ' . $e->getMessage());
            }
        }

        log_message('info', 'Added foreign key constraints');
    } catch (Exception $e) {
        log_message('error', 'Failed to add foreign key constraints: ' . $e->getMessage());
    }
}

/**
 * Add performance indexes
 */
function pos_inventory_add_performance_indexes()
{
    $CI = &get_instance();

    try {
        $indexes = [
            'ALTER TABLE `' . db_prefix() . 'pos_transactions` ADD INDEX `idx_transaction_date` (`created_at`)',
            'ALTER TABLE `' . db_prefix() . 'pos_transactions` ADD INDEX `idx_transaction_type_date` (`transaction_type`, `created_at`)',
            'ALTER TABLE `' . db_prefix() . 'pos_stock_movements` ADD INDEX `idx_movement_date` (`created_at`)',
            'ALTER TABLE `' . db_prefix() . 'pos_stock_movements` ADD INDEX `idx_product_location` (`product_id`, `location_id`)',
            'ALTER TABLE `' . db_prefix() . 'pos_sync_logs` ADD INDEX `idx_integration_date` (`integration_id`, `started_at`)'
        ];

        foreach ($indexes as $index) {
            try {
                $CI->db->query($index);
            } catch (Exception $e) {
                // Index may already exist, log but continue
                log_message('warning', 'Index may already exist: ' . $e->getMessage());
            }
        }

        log_message('info', 'Added performance indexes');
    } catch (Exception $e) {
        log_message('error', 'Failed to add performance indexes: ' . $e->getMessage());
    }
}

/**
 * Insert default data
 */
function pos_inventory_insert_default_data()
{
    $CI = &get_instance();

    try {
        // Insert default location
        if ($CI->db->count_all_results(db_prefix() . 'pos_locations') == 0) {
            $CI->db->insert(db_prefix() . 'pos_locations', [
                'name' => 'Main Store',
                'address' => 'Main Store Address',
                'phone' => '',
                'email' => '',
                'manager_id' => get_staff_user_id(),
                'is_default' => 1,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            log_message('info', 'Created default location');
        }

        // Insert default category
        if ($CI->db->count_all_results(db_prefix() . 'pos_categories') == 0) {
            $CI->db->insert(db_prefix() . 'pos_categories', [
                'name' => 'General',
                'description' => 'General category for products',
                'parent_id' => null,
                'status' => 1,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            log_message('info', 'Created default category');
        }

        log_message('info', 'Default data insertion completed');
    } catch (Exception $e) {
        log_message('error', 'Failed to insert default data: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Create default options
 */
function pos_inventory_create_default_options()
{
    $CI = &get_instance();

    $default_options = [
        'pos_inventory_enable_pos' => '1',
        'pos_inventory_enable_inventory_tracking' => '1',
        'pos_inventory_enable_multi_location' => '1',
        'pos_inventory_enable_barcode_scanner' => '1',
        'pos_inventory_default_payment_method' => 'cash',
        'pos_inventory_tax_inclusive' => '0',
        'pos_inventory_auto_print_receipt' => '0',
        'pos_inventory_email_receipt_to_customer' => '0',
        'pos_inventory_receipt_header' => 'Thank you for your purchase!',
        'pos_inventory_receipt_footer' => 'Please come again!',
        'pos_inventory_default_location' => '1',
        'pos_inventory_low_stock_threshold' => '5',
        'pos_inventory_currency' => 'USD',
        'pos_inventory_tax_rate' => '0.00'
    ];

    foreach ($default_options as $name => $value) {
        $existing = $CI->db->get_where(db_prefix() . 'options', ['name' => $name])->row();
        if (!$existing) {
            $CI->db->insert(db_prefix() . 'options', [
                'name' => $name,
                'value' => $value,
                'autoload' => 1
            ]);
        }
    }

    log_message('info', 'Created default options');
}

/**
 * Verify installation
 */
function pos_inventory_verify_installation()
{
    $CI = &get_instance();

    // Only check for core essential tables
    $core_tables = [
        'pos_categories',
        'pos_products',
        'pos_locations',
        'pos_inventory'
    ];

    $missing_core_tables = [];
    foreach ($core_tables as $table) {
        if (!$CI->db->table_exists(db_prefix() . $table)) {
            $missing_core_tables[] = $table;
        }
    }

    // Check for optional tables but don't fail if missing
    $optional_tables = [
        'pos_product_variants',
        'pos_stock_movements',
        'pos_suppliers',
        'pos_purchase_orders',
        'pos_purchase_order_items',
        'pos_transactions',
        'pos_transaction_items',
        'pos_held_orders',
        'pos_integrations',
        'pos_sync_logs'
    ];

    $missing_optional_tables = [];
    foreach ($optional_tables as $table) {
        if (!$CI->db->table_exists(db_prefix() . $table)) {
            $missing_optional_tables[] = $table;
        }
    }

    // Only fail if core tables are missing
    if (!empty($missing_core_tables)) {
        throw new Exception('Installation verification failed. Missing core tables: ' . implode(', ', $missing_core_tables));
    }

    // Log warnings for missing optional tables
    if (!empty($missing_optional_tables)) {
        log_message('warning', 'Some optional tables are missing: ' . implode(', ', $missing_optional_tables));
    }

    log_message('info', 'Installation verification passed (core tables present)');
}

/**
 * Create migration record
 */
function pos_inventory_create_migration_record()
{
    $CI = &get_instance();

    try {
        // Check if migration record already exists
        $existing = $CI->db->get_where(db_prefix() . 'migrations', ['version' => 100])->row();

        if (!$existing) {
            // Check available columns in migrations table
            $migration_fields = $CI->db->field_data(db_prefix() . 'migrations');
            $available_columns = [];
            foreach ($migration_fields as $field) {
                $available_columns[] = $field->name;
            }

            // Start with only the version (which should always exist)
            $migration_data = [];

            // Add columns only if they exist in the table
            if (in_array('version', $available_columns)) {
                $migration_data['version'] = 100;
            }

            if (in_array('time', $available_columns)) {
                $migration_data['time'] = time();
            }

            if (in_array('timestamp', $available_columns)) {
                $migration_data['timestamp'] = time();
            }

            if (in_array('batch', $available_columns)) {
                $migration_data['batch'] = 1;
            }

            if (in_array('class', $available_columns)) {
                $migration_data['class'] = 'Migration_Version_100';
            }

            if (in_array('namespace', $available_columns)) {
                $migration_data['namespace'] = '';
            }

            if (in_array('group', $available_columns)) {
                $migration_data['`group`'] = 'pos_inventory';
            }

            // Only insert if we have at least the version
            if (!empty($migration_data)) {
                try {
                    $CI->db->insert(db_prefix() . 'migrations', $migration_data);
                    log_message('info', 'Created migration record for version 100');
                } catch (Exception $insert_e) {
                    log_message('error', 'Failed to create migration record: ' . $insert_e->getMessage());
                }
            }
        } else {
            log_message('info', 'Migration record already exists for version 100');
        }
    } catch (Exception $e) {
        log_message('error', 'Failed to create migration record: ' . $e->getMessage());
        // Don't throw here as this is not critical for functionality
    }
}
