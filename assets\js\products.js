/**
 * POS Inventory Module - Products JavaScript
 * Handles product-specific functionality
 */

// Ensure jQuery is available
if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
    var $ = jQuery;
}

var Products = Products || {};

/**
 * Product form functionality
 */
Products.Form = {
    
    /**
     * Initialize product form
     */
    init: function() {
        this.initPriceCalculations();
        this.initInventoryToggle();
        this.initVariants();
        this.initImageUpload();
        this.initFormValidation();
    },
    
    /**
     * Initialize price calculations
     */
    initPriceCalculations: function() {
        // Calculate profit margin
        function calculateProfit() {
            var costPrice = parseFloat($('#cost_price').val()) || 0;
            var salePrice = parseFloat($('#sale_price').val()) || 0;
            
            if (costPrice > 0 && salePrice > 0) {
                var profit = salePrice - costPrice;
                var margin = (profit / salePrice) * 100;
                
                $('#profit-amount').text((window.currency_symbol || '$') + profit.toFixed(2));
                $('#profit-margin').text(margin.toFixed(2) + '%');
                
                // Update margin color based on value
                var marginElement = $('#profit-margin');
                marginElement.removeClass('text-success text-warning text-danger');
                if (margin >= 30) {
                    marginElement.addClass('text-success');
                } else if (margin >= 15) {
                    marginElement.addClass('text-warning');
                } else {
                    marginElement.addClass('text-danger');
                }
            } else {
                $('#profit-amount').text((window.currency_symbol || '$') + '0.00');
                $('#profit-margin').text('0%');
            }
        }
        
        $('#cost_price, #sale_price').on('input', calculateProfit);
        calculateProfit(); // Initial calculation
    },
    
    /**
     * Initialize inventory tracking toggle
     */
    initInventoryToggle: function() {
        $('#track_inventory').change(function() {
            if ($(this).is(':checked')) {
                $('.inventory-fields').show();
                $('#initial_stock').prop('required', true);
            } else {
                $('.inventory-fields').hide();
                $('#initial_stock').prop('required', false);
            }
        });
        
        // Trigger initial state
        $('#track_inventory').trigger('change');
    },
    
    /**
     * Initialize product variants
     */
    initVariants: function() {
        var variantCount = 0;
        
        // Add variant button
        $('#add-variant').click(function() {
            Products.Form.addVariantRow();
        });
        
        // Remove variant button
        $(document).on('click', '.remove-variant', function() {
            $(this).closest('.variant-row').remove();
        });
    },
    
    /**
     * Add variant row
     */
    addVariantRow: function(variant) {
        variant = variant || {};
        var variantCount = $('.variant-row').length;
        
        var html = '<div class="variant-row panel panel-default" style="margin-bottom: 10px;">' +
            '<div class="panel-body">' +
                '<div class="row">' +
                    '<div class="col-md-3">' +
                        '<div class="form-group">' +
                            '<label>Variant Name</label>' +
                            '<input type="text" class="form-control" name="variants[' + variantCount + '][name]" value="' + (variant.name || '') + '" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<div class="form-group">' +
                            '<label>SKU</label>' +
                            '<input type="text" class="form-control" name="variants[' + variantCount + '][sku]" value="' + (variant.sku || '') + '" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<div class="form-group">' +
                            '<label>Cost Price</label>' +
                            '<input type="number" step="0.01" class="form-control" name="variants[' + variantCount + '][cost_price]" value="' + (variant.cost_price || '') + '">' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<div class="form-group">' +
                            '<label>Sale Price</label>' +
                            '<input type="number" step="0.01" class="form-control" name="variants[' + variantCount + '][sale_price]" value="' + (variant.sale_price || '') + '" required>' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-2">' +
                        '<div class="form-group">' +
                            '<label>Weight</label>' +
                            '<input type="number" step="0.01" class="form-control" name="variants[' + variantCount + '][weight]" value="' + (variant.weight || '') + '">' +
                        '</div>' +
                    '</div>' +
                    '<div class="col-md-1">' +
                        '<div class="form-group">' +
                            '<label>&nbsp;</label>' +
                            '<button type="button" class="btn btn-danger btn-block remove-variant">' +
                                '<i class="fa fa-trash"></i>' +
                            '</button>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>';
        
        $('#variants-container').append(html);
    },
    
    /**
     * Initialize image upload
     */
    initImageUpload: function() {
        $('#product_image').change(function() {
            var file = this.files[0];
            if (file) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#image-preview').html('<img src="' + e.target.result + '" class="img-responsive" style="max-width: 200px;">');
                };
                reader.readAsDataURL(file);
            }
        });
    },
    
    /**
     * Initialize form validation
     */
    initFormValidation: function() {
        $('#product-form').submit(function(e) {
            var isValid = true;
            
            // Validate required fields
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    $(this).addClass('has-error');
                    isValid = false;
                } else {
                    $(this).removeClass('has-error');
                }
            });
            
            // Validate SKU uniqueness (you might want to add AJAX check here)
            var sku = $('#sku').val();
            if (sku && sku.length < 3) {
                $('#sku').addClass('has-error');
                alert('SKU must be at least 3 characters long');
                isValid = false;
            }
            
            // Validate prices
            var costPrice = parseFloat($('#cost_price').val()) || 0;
            var salePrice = parseFloat($('#sale_price').val()) || 0;
            
            if (salePrice <= 0) {
                $('#sale_price').addClass('has-error');
                alert('Sale price must be greater than 0');
                isValid = false;
            }
            
            if (costPrice > salePrice) {
                $('#cost_price').addClass('has-error');
                alert('Cost price cannot be greater than sale price');
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
                return false;
            }
        });
    }
};

/**
 * Product list functionality
 */
Products.List = {
    
    /**
     * Initialize product list
     */
    init: function() {
        this.initDataTable();
        this.initFilters();
        this.initBulkActions();
    },
    
    /**
     * Initialize DataTable
     */
    initDataTable: function() {
        if ($('#products-table').length) {
            $('#products-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: admin_url + 'pos_inventory/products_table',
                    type: 'POST',
                    data: function(d) {
                        d.category = $('#filter_category').val();
                        d.status = $('#filter_status').val();
                        d.stock_status = $('#filter_stock_status').val();
                    }
                },
                columns: [
                    { data: 'checkbox', orderable: false, searchable: false },
                    { data: 'image', orderable: false, searchable: false },
                    { data: 'name' },
                    { data: 'sku' },
                    { data: 'category_name' },
                    { data: 'sale_price' },
                    { data: 'cost_price' },
                    { data: 'stock_quantity' },
                    { data: 'status' },
                    { data: 'actions', orderable: false, searchable: false }
                ],
                order: [[2, 'asc']],
                pageLength: 25
            });
        }
    },
    
    /**
     * Initialize filters
     */
    initFilters: function() {
        $('#filter_category, #filter_status, #filter_stock_status').change(function() {
            $('#products-table').DataTable().ajax.reload();
        });
    },
    
    /**
     * Initialize bulk actions
     */
    initBulkActions: function() {
        $('#bulk-action-btn').click(function() {
            var action = $('#bulk_action').val();
            var selected = [];
            
            $('.product-checkbox:checked').each(function() {
                selected.push($(this).val());
            });
            
            if (selected.length === 0) {
                alert('Please select at least one product');
                return;
            }
            
            if (confirm('Are you sure you want to perform this action on ' + selected.length + ' product(s)?')) {
                Products.List.performBulkAction(action, selected);
            }
        });
    },
    
    /**
     * Perform bulk action
     */
    performBulkAction: function(action, productIds) {
        $.post(admin_url + 'pos_inventory/bulk_products', {
            action: action,
            product_ids: productIds
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#products-table').DataTable().ajax.reload();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
};

// Initialize when document is ready
$(document).ready(function() {
    // Check if we're on a product page
    if ($('#product-form').length) {
        Products.Form.init();
    }
    
    if ($('#products-table').length) {
        Products.List.init();
    }
});

// Export for global use
window.Products = Products;
